# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/flashbookkeeping?schema=public"

# 企业微信配置
WECOM_CORP_ID="your_corp_id"
WECOM_TOKEN="your_token"
WECOM_ENCODING_AES_KEY="your_encoding_aes_key"
WECOM_SECRET="your_secret"

# 豆包LLM配置
DOUBAO_API_KEY="your_doubao_api_key"
DOUBAO_API_ENDPOINT="https://ark.cn-beijing.volces.com/api/v3"

# Notion配置（公开集成）
# 在 https://www.notion.so/my-integrations 创建公开集成
# 设置重定向URL为: http://localhost:3000/notion/callback
NOTION_CLIENT_ID="your_notion_client_id"
NOTION_CLIENT_SECRET="your_notion_client_secret"
NOTION_REDIRECT_URI="http://localhost:3000/notion/callback"

# 飞书配置
FEISHU_APP_ID="your_feishu_app_id"
FEISHU_APP_SECRET="your_feishu_app_secret"
FEISHU_REDIRECT_URI="http://localhost:3000/api/feishu/callback"

# 应用配置
PORT=3000
NODE_ENV="development"

# JWT配置
JWT_SECRET="your_jwt_secret_key"

# 加密配置
ENCRYPTION_KEY="32_character_long_encryption_key_here"

# Redis配置（用于消息队列）
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# 支付配置
WECHAT_PAY_APP_ID="your_wechat_pay_app_id"
WECHAT_PAY_MCH_ID="your_wechat_pay_mch_id"
WECHAT_PAY_KEY="your_wechat_pay_key"

# 日志配置
LOG_LEVEL="info"
LOG_FILE_PATH="./logs"

# 免费用户限制
FREE_USER_MONTHLY_LIMIT=30
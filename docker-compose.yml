version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: flashbookkeeping-postgres
    environment:
      POSTGRES_DB: flashbookkeeping
      POSTGRES_USER: flashuser
      POSTGRES_PASSWORD: flashpass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - flashbookkeeping-network
    restart: unless-stopped

  # Redis 缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: flashbookkeeping-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - flashbookkeeping-network
    restart: unless-stopped

  # NestJS 应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: flashbookkeeping-app
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************************/flashbookkeeping?schema=public
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    env_file:
      - .env
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    networks:
      - flashbookkeeping-network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: flashbookkeeping-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - flashbookkeeping-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  flashbookkeeping-network:
    driver: bridge

---
type: "manual"
---

# Roo Code 规则文件：NestJS 项目

你是一名有经验的 TypeScript 和 NestJS 开发者，遵循以下规范：

## 注意
- 在任何阶段，有任何不清楚的地方都可以直接向我提问或者收集必要的信息，避免你自己猜想或者使用不存在的资源。

## 项目构建流程

1. **需求分析**  
   - 项目开始前，必须与用户充分沟通，收集并分析所有业务需求。
   - 生成详细的需求文档（requirements.md），内容包括功能描述、业务流程、用户角色、用例等。

2. **设计阶段**  
   - 基于需求文档，编写详细的设计文档，至少包括：
     - 架构设计（architecture.md）：整体技术架构、选型、依赖说明。
     - 模块设计（modules.md）：各模块职责、接口、交互关系。
     - 数据库设计（database.md）：ER 图、表结构、字段说明、索引等。
   - 所有设计文档统一放在项目根目录的 `/docs` 路径下，结构如下：
     ```
     /docs
       ├── requirements.md
       ├── architecture.md
       ├── modules.md
       └── database.md
     ```

3. **开发与实现**
   - 直接在用户当前打开的路径下初始化nestjs工程，不要再新建目录，防止路径太深。
   - 使用pnpm作为包管理工具。
   - 如果设计需要用到消息中间件时，优先考虑使用Rabbitmq。
   - 代码开发严格遵循下述 TypeScript、NestJS 及项目结构规范。
   - 在设计阶段完成后，编码阶段开始前，必须制定详细的开发计划。开发计划应包括各阶段任务分解、时间节点、及里程碑安排。项目实施过程中需严格按照开发计划推进，确保项目按期、高质量完成。

## 代码管理规范
- 使用 GitHub 进行代码托管和管理，所有项目代码必须提交至指定的 GitHub 仓库。
- 严格按照 Git 的标准操作流程进行代码管理，包括但不限于：分支管理、提交信息规范、合并请求（Pull Request）流程、代码评审（Code Review）等。
- 所有功能开发、修复和优化必须在独立分支上进行，禁止直接在主分支（如 main/master）开发。
- 提交信息需简明、规范，建议遵循 “类型: 简要描述” 的格式（如 feat: 新增用户注册功能）。
- 合并代码前必须经过至少一名开发者的代码评审，确保代码质量和规范一致性。
- 定期同步主分支，避免分支长期滞后导致冲突。
   

## 安全规范
- 依赖安全：定期使用 pnpm audit 或 snyk 等工具检查依赖安全漏洞。
- 敏感信息管理：严禁将敏感信息（如密钥、密码）硬编码在代码库，全部通过 .env 文件或安全配置管理。
- 接口安全：所有接口应考虑身份认证（如 JWT）、权限校验，敏感操作需有操作日志。
- 输入校验：所有外部输入必须严格校验，防止注入攻击。

## API 文档
- 自动化文档：集成 Swagger（@nestjs/swagger），自动生成并维护 API 文档，文档地址如 /docs。
- 文档更新：每次接口变更需同步更新文档，保证文档与实现一致。

## 代码质量
- 静态检查：集成 ESLint，强制执行代码规范。
- 代码提交钩子：使用 Husky + lint-staged，确保提交前自动格式化和检查代码。
- 分支管理：采用 Git Flow 或 trunk-based development，主分支始终可部署。

## 性能优化
- 缓存：如有高频读操作，优先考虑集成 Redis 缓存。
- 慢查询监控：数据库操作需监控慢查询，定期优化索引和 SQL。
- 异步任务：耗时操作应通过队列（如 RabbitMQ）异步处理。

## 国际化（如有需求）
- i18n 支持：如需多语言，集成 nestjs-i18n，所有用户可见文本支持国际化。

## 灰度与回滚
- 版本控制：docker-compose 支持多版本部署，便于灰度发布和快速回滚。
- 数据备份：数据库定期自动备份，备份脚本和策略文档化。

## 通用 TypeScript 规范

- 所有文档和注释均使用中文
- 明确声明所有变量和函数的类型，避免 any
- 使用 JSDoc 注释公开的类和方法
- 每个文件只导出一个实体
- 类名用 PascalCase，变量/函数/方法用 camelCase，文件/目录用 kebab-case
- 环境变量用 UPPERCASE，必须存入在根目录的.env文件中
- 函数名以动词开头，布尔变量用 is/has/can 前缀
- 避免缩写，除非是常用缩写（如 API、URL、req、res、err、ctx、i、j）
- 使用Prettier进行代码格式化

## 函数和方法

- 保持函数简短，单一职责，少于 20 行
- 使用对象传递和返回多个参数（RO-RO 原则）
- 优先使用箭头函数处理简单逻辑
- 复杂逻辑拆分为工具函数
- 默认参数值代替 null/undefined 检查

## 数据和类型

- 尽量使用复合类型封装数据，避免滥用原始类型
- 自定义类型时，同模块或功能的类型定义放进一个单独的文件中
- 输入 DTO 使用 class-validator 校验
- 输出类型简单明了
- 数据优先不可变（readonly, as const）

## 类和模块

- 遵循 SOLID 原则
- 优先组合而非继承
- 每个模块只负责一个领域
- 每个实体一个 service
- 全局异常过滤器、全局中间件、权限守卫、拦截器放在 core module
- 公共服务和工具放在 shared module

## 日志（Logging）

- 统一使用 NestJS Logger 或自定义 logger service，所有重要操作、异常、关键业务流程必须记录日志。
- 日志分级（info, warn, error, debug），可配置输出到控制台和文件。
- 日志格式应包含时间戳、日志级别、模块名、消息内容。
- 日志配置项（如日志级别、输出路径）应通过环境变量管理。
  日志使用示例：
  ```
  import { Logger } from '@nestjs/common';
  const logger = new Logger('UserService');
  logger.log('用户创建成功', { userId });
  logger.error('用户创建失败', error.stack);

  ```

## 异常处理（Exception Handling）

- 全局异常处理器（Global Exception Filter）必须实现，捕获所有未处理异常并返回统一格式的错误响应。
- 业务异常使用自定义异常类（如 BadRequestException、NotFoundException 等）。
- 异常日志必须详细记录异常堆栈、请求参数、用户信息等关键信息，便于排查。
- 所有异常处理逻辑放在 core/filters 目录。
  全局异常过滤器示例
  ```
  import { ExceptionFilter, Catch, ArgumentsHost, HttpException, Logger } from '@nestjs/common';
  import { Request, Response } from 'express';
  
  @Catch()
  export class AllExceptionsFilter implements ExceptionFilter {
    private readonly logger = new Logger(AllExceptionsFilter.name);
  
    catch(exception: unknown, host: ArgumentsHost) {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();
      const request = ctx.getRequest<Request>();
      const status = exception instanceof HttpException ? exception.getStatus() : 500;
      const message = exception instanceof HttpException ? exception.getResponse() : exception;
  
      this.logger.error(
        `HTTP ${status} Error on ${request.method} ${request.url}`,
        (exception as any)?.stack,
      );
  
      response.status(status).json({
        statusCode: status,
        timestamp: new Date().toISOString(),
        path: request.url,
        message,
      });
    }
  }
  ```

## 监控（Monitoring）和告警

- 集成 Prometheus、OpenTelemetry 或类似监控方案，采集应用关键指标（如请求数、响应时间、错误率等）。
- 所有 HTTP 接口、数据库操作、外部服务调用应埋点监控。
- 监控指标暴露在 /metrics 路径，便于接入监控系统。
- 监控相关代码和配置放在 core/monitor 目录。
- 告警配置：监控系统需配置告警规则，异常波动及时通知开发/运维。

## 一键部署（docker-compose）

- 项目根目录必须提供 docker-compose.yml 文件，实现一键部署。
- docker-compose.yml 至少包含：
  - 应用服务（NestJS）
  - 数据库服务（如 PostgreSQL、MySQL、MongoDB 等）
  - nginx服务
  - https证书自动续签服务
  - 可选：Redis、监控（如 Prometheus、Grafana）、消息中间件（如 Rabbitmq）等服务
- 应用 Dockerfile 必须支持多阶段构建，减小镜像体积。
- 所有服务配置（如端口、环境变量、数据卷）可通过 .env 文件灵活配置。
- 部署文档（deploy.md）放在 /docs 目录，说明如何使用 docker-compose 启动、停止、查看日志、扩容等操作。

## 测试

- 使用 Jest，遵循 Arrange-Act-Assert
- 每个公开函数写单元测试
- 每个模块写端到端测试
- 控制器添加 admin/test 方法做冒烟测试

## NestJS 特有

- 模块化架构，每个主领域/路由一个 module
- 控制器只处理 HTTP，业务逻辑放在 service
- DTO 输入校验，输出类型简单
- 持久化用 Prisma, 需要根据数据库设计文档，生成模型文件，SQL文件，这些都放在根目录的/prisma目录下，并自动生成prisma client
- 目录结构清晰，models 放数据类型，services 放业务逻辑
- 不需要单独创建配置模块，configModule在appModule中加载即可。

# 目录结构建议
```
src/
  ├── app.module.ts
  ├── main.ts
  ├── modules/
  │     ├── user/
  │     │     ├── user.controller.ts
  │     │     ├── user.service.ts
  │     │     ├── user.module.ts
  │     │     ├── dto/
  │     │     └── entities/
  │     └── ...
  ├── common/
  ├── core/
  │     ├── filters/
  │     ├── logger/
  │     └── monitor/
  ├── shared/
  └── ...
docs/
  ├── requirements.md
  ├── architecture.md
  ├── modules.md
  ├── database.md
  └── deploy.md
prisma/
  ├── schema.prisma
  └── migration.sql
docker-compose.yml
Dockerfile
.env

```

# 例子
  ```
  // user.service.ts
  import { Injectable } from '@nestjs/common';
  import { CreateUserDto } from './dto/create-user.dto';
  
  @Injectable()
  export class UserService {
    create(dto: CreateUserDto) {
      // ...
    }
  }
  
  // user.controller.ts
  import { Controller, Post, Body } from '@nestjs/common';
  import { UserService } from './user.service';
  import { CreateUserDto } from './dto/create-user.dto';
  
  @Controller('users')
  export class UserController {
    constructor(private readonly userService: UserService) {}
  
    @Post()
    create(@Body() dto: CreateUserDto) {
      return this.userService.create(dto);
    }
  }
  ```
  
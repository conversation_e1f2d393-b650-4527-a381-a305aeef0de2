import { Module } from '@nestjs/common';
import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { UserModule } from './modules/user/user.module';
import { BindingModule } from './modules/binding/binding.module';
import { WecomModule } from './modules/wecom.module';
import { AiModule } from './modules/ai/ai.module';
import { NotionModule } from './modules/notion/notion.module';
import { FeishuModule } from './modules/feishu/feishu.module';

@Module({
  imports: [
    PrismaModule,
    UserModule,
    BindingModule,
    WecomModule,
    AiModule,
    NotionModule,
    FeishuModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}

import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

export interface MessageJob {
  userId: string;
  kfId: string;
  messageId: string;
  messageType: string;
  content: string;
  mediaId?: string;
  timestamp: number;
}

export interface AiAnalysisJob {
  userId: string;
  kfId: string;
  messageId: string;
  messageType: string;
  content: string;
  mediaUrl?: string;
}

export interface DataSyncJob {
  userId: string;
  billingInfo: {
    amount: number;
    category: string;
    date: string;
    description: string;
    confidence: number;
  };
  platforms: string[];
}

@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue('message-processing') private messageQueue: Queue,
    @InjectQueue('ai-analysis') private aiQueue: Queue,
    @InjectQueue('data-sync') private syncQueue: Queue,
  ) {}

  /**
   * 添加消息处理任务
   */
  async addMessageProcessingJob(data: MessageJob): Promise<void> {
    try {
      await this.messageQueue.add('process-message', data, {
        priority: 10,
        delay: 0,
      });
      this.logger.log(`消息处理任务已添加: ${data.messageId}`);
    } catch (error) {
      this.logger.error('添加消息处理任务失败:', error);
      throw error;
    }
  }

  /**
   * 添加AI分析任务
   */
  async addAiAnalysisJob(data: AiAnalysisJob): Promise<void> {
    try {
      await this.aiQueue.add('analyze-message', data, {
        priority: 5,
        delay: 1000, // 延迟1秒执行，给消息处理时间
      });
      this.logger.log(`AI分析任务已添加: ${data.messageId}`);
    } catch (error) {
      this.logger.error('添加AI分析任务失败:', error);
      throw error;
    }
  }

  /**
   * 添加数据同步任务
   */
  async addDataSyncJob(data: DataSyncJob): Promise<void> {
    try {
      await this.syncQueue.add('sync-data', data, {
        priority: 3,
        delay: 2000, // 延迟2秒执行，确保AI分析完成
      });
      this.logger.log(`数据同步任务已添加: ${data.userId}`);
    } catch (error) {
      this.logger.error('添加数据同步任务失败:', error);
      throw error;
    }
  }

  /**
   * 获取队列状态
   */
  async getQueueStats() {
    try {
      const messageStats = await this.messageQueue.getJobCounts();
      const aiStats = await this.aiQueue.getJobCounts();
      const syncStats = await this.syncQueue.getJobCounts();

      return {
        messageProcessing: messageStats,
        aiAnalysis: aiStats,
        dataSync: syncStats,
      };
    } catch (error) {
      this.logger.error('获取队列状态失败:', error);
      throw error;
    }
  }

  /**
   * 清理失败的任务
   */
  async cleanFailedJobs(): Promise<void> {
    try {
      await this.messageQueue.clean(24 * 60 * 60 * 1000, 'failed'); // 清理24小时前的失败任务
      await this.aiQueue.clean(24 * 60 * 60 * 1000, 'failed');
      await this.syncQueue.clean(24 * 60 * 60 * 1000, 'failed');
      this.logger.log('失败任务清理完成');
    } catch (error) {
      this.logger.error('清理失败任务出错:', error);
    }
  }
}

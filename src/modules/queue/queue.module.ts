import { Module, forwardRef } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  MessageProcessor,
  AiAnalysisProcessor,
  DataSyncProcessor
} from './processors/message.processor';
import { QueueService } from './queue.service';
import { QueueController } from './queue.controller';
import { AiModule } from '../ai/ai.module';
import { NotionModule } from '../notion/notion.module';
import { FeishuModule } from '../feishu/feishu.module';
import { BindingModule } from '../binding/binding.module';

@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        redis: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          password: configService.get('REDIS_PASSWORD'),
          db: configService.get('REDIS_DB', 0),
          retryDelayOnFailover: 100,
          enableReadyCheck: false,
          maxRetriesPerRequest: 3,
          lazyConnect: true,
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: 'message-processing',
    }),
    BullModule.registerQueue({
      name: 'ai-analysis',
    }),
    BullModule.registerQueue({
      name: 'data-sync',
    }),
    AiModule,
    NotionModule,
    FeishuModule,
    BindingModule,
    forwardRef(() => import('../wecom/wecom.module').then(m => m.WecomModule)),
  ],
  controllers: [QueueController],
  providers: [
    MessageProcessor,
    AiAnalysisProcessor,
    DataSyncProcessor,
    QueueService
  ],
  exports: [QueueService],
})
export class QueueModule {}

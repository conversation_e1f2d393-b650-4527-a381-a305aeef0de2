import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

// 定义豆包 API 返回的数据结构
interface DoubaoResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

// 定义账单信息的接口
export interface BillingInfo {
  amount: number;
  category: string;
  date: string;
  description: string;
  confidence?: number; // 置信度
  error?: string;
}

// 定义支持的消息类型
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VOICE = 'voice',
  FILE = 'file',
}

// 定义消息内容接口
export interface MessageContent {
  type: MessageType;
  content: string;
  url?: string; // 图片、语音、文件的URL
  mimeType?: string; // 文件类型
}

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 从多模态内容中提取账单信息
   * @param content 消息内容
   * @returns 提取的账单信息
   */
  async extractBillingInfo(content: MessageContent): Promise<BillingInfo> {
    const apiKey = this.configService.get<string>('DOUBAO_API_KEY');
    const endpoint = this.configService.get<string>('DOUBAO_API_ENDPOINT');

    if (!apiKey || !endpoint) {
      this.logger.error('豆包 API 的 Key 或 Endpoint 未配置');
      return {
        error: 'AI 服务未正确配置',
        amount: 0,
        category: '',
        date: '',
        description: '',
      };
    }

    try {
      // 根据内容类型选择处理方式
      switch (content.type) {
        case MessageType.TEXT:
          return await this.processTextContent(content.content);
        case MessageType.IMAGE:
          return await this.processImageContent(content.url!);
        case MessageType.VOICE:
          return await this.processVoiceContent(content.url!);
        case MessageType.FILE:
          return await this.processFileContent(content.url!, content.mimeType);
        default:
          return this.createErrorResponse('不支持的消息类型');
      }
    } catch (error) {
      this.logger.error('提取账单信息失败:', error);
      return this.createErrorResponse('处理消息时发生错误');
    }
  }

  /**
   * 处理文本内容
   */
  private async processTextContent(text: string): Promise<BillingInfo> {
    const prompt = this.buildPrompt('text');
    return await this.callDoubaoAPI(prompt, text);
  }

  /**
   * 处理图片内容
   */
  private async processImageContent(imageUrl: string): Promise<BillingInfo> {
    const prompt = this.buildPrompt('image');
    return await this.callDoubaoAPI(prompt, '', imageUrl);
  }

  /**
   * 处理语音内容（需要先转文字）
   */
  private async processVoiceContent(voiceUrl: string): Promise<BillingInfo> {
    // TODO: 实现语音转文字功能
    this.logger.warn('语音处理功能暂未实现');
    return this.createErrorResponse('语音处理功能暂未实现');
  }

  /**
   * 处理文件内容
   */
  private async processFileContent(
    fileUrl: string,
    mimeType?: string,
  ): Promise<BillingInfo> {
    // TODO: 根据文件类型处理（PDF、Excel等）
    this.logger.warn('文件处理功能暂未实现');
    return this.createErrorResponse('文件处理功能暂未实现');
  }

  /**
   * 构建提示词
   */
  private buildPrompt(contentType: string): string {
    const basePrompt = `
你是一个专业的智能记账助手。请从${contentType === 'image' ? '图片' : '文本'}中提取关键的记账信息。

要求：
1. 必须返回有效的JSON格式
2. 如果信息不完整，请根据常识进行合理推断
3. 如果完全无法识别，请在error字段中说明原因
4. 金额必须是数字类型，不要包含货币符号
5. 日期格式必须是YYYY-MM-DD，如果没有日期信息，使用今天的日期

返回格式：
{
  "amount": 数字,
  "category": "餐饮|交通|购物|娱乐|住房|医疗|教育|其他",
  "date": "YYYY-MM-DD",
  "description": "详细描述",
  "confidence": 0.0-1.0,
  "error": "错误信息(可选)"
}

常见类别说明：
- 餐饮: 吃饭、喝咖啡、外卖等
- 交通: 打车、地铁、公交、加油等
- 购物: 买衣服、日用品、电子产品等
- 娱乐: 电影、游戏、旅游等
- 住房: 房租、水电费、物业费等
- 医疗: 看病、买药、体检等
- 教育: 学费、培训、书籍等
- 其他: 无法归类的支出
    `;

    return basePrompt;
  }

  /**
   * 调用豆包API
   */
  private async callDoubaoAPI(
    prompt: string,
    text: string,
    imageUrl?: string,
  ): Promise<BillingInfo> {
    const apiKey = this.configService.get<string>('DOUBAO_API_KEY');
    const endpoint = this.configService.get<string>('DOUBAO_API_ENDPOINT');

    if (!apiKey || !endpoint) {
      this.logger.error('豆包 API 配置缺失');
      return this.createErrorResponse('AI 服务未正确配置');
    }

    const userContent = [];
    if (text) {
      userContent.push({ type: 'text', text });
    }
    if (imageUrl) {
      userContent.push({ type: 'image_url', image_url: { url: imageUrl } });
    }

    const requestBody = {
      model: 'doubao-pro-32k',
      messages: [
        {
          role: 'system',
          content: prompt,
        },
        {
          role: 'user',
          content: userContent,
        },
      ],
      temperature: 0.1, // 降低随机性，提高一致性
      max_tokens: 1000,
      stream: false,
    };

    try {
      const { data } = await firstValueFrom(
        this.httpService.post<DoubaoResponse>(endpoint, requestBody, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30秒超时
        }),
      );

      const responseContent = data.choices[0]?.message?.content;
      if (!responseContent) {
        return this.createErrorResponse('AI 服务返回空内容');
      }

      return this.parseAIResponse(responseContent);
    } catch (error) {
      this.logger.error('调用豆包 API 失败:', error);
      return this.createErrorResponse('调用大模型服务失败');
    }
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(content: string): BillingInfo {
    try {
      // 尝试提取JSON部分
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? jsonMatch[0] : content;

      const parsed = JSON.parse(jsonStr) as BillingInfo;

      // 验证必要字段
      if (typeof parsed.amount !== 'number' || parsed.amount < 0) {
        return this.createErrorResponse('金额格式不正确');
      }

      if (!parsed.category || !parsed.description) {
        return this.createErrorResponse('缺少必要的账单信息');
      }

      // 设置默认日期
      if (!parsed.date) {
        parsed.date = new Date().toISOString().split('T')[0];
      }

      // 设置默认置信度
      if (typeof parsed.confidence !== 'number') {
        parsed.confidence = 0.8;
      }

      return parsed;
    } catch (error) {
      this.logger.error('解析AI响应失败:', error);
      this.logger.debug('原始响应内容:', content);
      return this.createErrorResponse('无法解析AI服务返回的数据');
    }
  }

  /**
   * 创建错误响应
   */
  private createErrorResponse(errorMessage: string): BillingInfo {
    return {
      error: errorMessage,
      amount: 0,
      category: '其他',
      date: new Date().toISOString().split('T')[0],
      description: '处理失败',
      confidence: 0,
    };
  }

  /**
   * 验证账单信息
   */
  async validateBillingInfo(billingInfo: BillingInfo): Promise<boolean> {
    if (billingInfo.error) {
      return false;
    }

    if (billingInfo.amount <= 0) {
      return false;
    }

    const validCategories = [
      '餐饮', '交通', '购物', '娱乐', '住房', '医疗', '教育', '其他'
    ];

    if (!validCategories.includes(billingInfo.category)) {
      return false;
    }

    // 验证日期格式
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(billingInfo.date)) {
      return false;
    }

    return true;
  }
}

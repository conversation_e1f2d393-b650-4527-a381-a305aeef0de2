import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

// 定义豆包 API 返回的数据结构
interface DoubaoResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

// 定义账单信息的接口
export interface BillingInfo {
  amount: number;
  category: string;
  date: string;
  description: string;
  error?: string;
}

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 从文本或图片中提取账单信息
   * @param message 文本消息
   * @param imageUrl 图片 URL
   * @returns 提取的账单信息
   */
  async extractBillingInfo(
    message: string,
    imageUrl?: string,
  ): Promise<BillingInfo> {
    const apiKey = this.configService.get<string>('DOUBAO_API_KEY');
    const endpoint = this.configService.get<string>('DOUBAO_API_ENDPOINT');

    if (!apiKey || !endpoint) {
      this.logger.error('豆包 API 的 Key 或 Endpoint 未配置');
      return {
        error: 'AI 服务未正确配置',
        amount: 0,
        category: '',
        date: '',
        description: '',
      };
    }

    const prompt = `
      你是一个智能记账助手。
      请从以下文本或图片中提取关键的记账信息，并以统一的 JSON 格式返回。
      如果信息不完整，请根据常识进行推断。
      如果无法识别，请返回一个带有 error 字段的 JSON 对象。

      需要提取的信息包括：
      - amount: 金额 (数字类型)
      - category: 类别 (字符串, 例如: 餐饮, 交通, 购物, 娱乐, 住房, 其他)
      - date: 日期 (YYYY-MM-DD 格式)
      - description: 描述 (字符串)

      这是需要处理的内容:
      ${message}
    `;

    const requestBody = {
      model: 'doubao-pro-32k',
      messages: [
        {
          role: 'system',
          content: prompt,
        },
        {
          role: 'user',
          content: [
            { type: 'text', text: message },
            ...(imageUrl
              ? [{ type: 'image_url', image_url: { url: imageUrl } }]
              : []),
          ],
        },
      ],
      stream: false,
    };

    try {
      const { data } = await firstValueFrom(
        this.httpService.post<DoubaoResponse>(endpoint, requestBody, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
        }),
      );

      const content = data.choices[0].message.content;
      // 尝试解析 JSON，如果失败则返回错误
      try {
        return JSON.parse(content) as BillingInfo;
      } catch {
        this.logger.error('解析豆包 API 返回的 JSON 失败', content);
        return {
          error: '无法解析 AI 服务返回的数据',
          amount: 0,
          category: '',
          date: '',
          description: '',
        };
      }
    } catch (error) {
      this.logger.error('调用豆包 API 出错:', (error as Error).stack);
      return {
        error: '调用大模型服务失败',
        amount: 0,
        category: '',
        date: '',
        description: '',
      };
    }
  }
}

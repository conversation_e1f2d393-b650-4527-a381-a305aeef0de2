import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError } from 'axios';
import { Client } from '@notionhq/client';

/**
 * Notion OAuth 返回的Token数据结构
 */
export interface NotionTokenResponse {
  access_token: string;
  token_type: string;
  bot_id: string;
  workspace_name?: string;
  workspace_icon?: string;
  workspace_id?: string;
  owner?: {
    type: string;
    user?: {
      object: string;
      id: string;
    };
  };
}

/**
 * 记账记录数据结构
 */
interface RecordData {
  amount: number;
  category: string;
  date: string;
  description: string;
}

@Injectable()
export class NotionService {
  private readonly logger = new Logger(NotionService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 获取Notion OAuth授权URL
   */
  getAuthUrl(): string {
    const clientId = this.configService.get<string>('NOTION_CLIENT_ID');
    const redirectUri = this.configService.get<string>('NOTION_REDIRECT_URI');

    if (!clientId || !redirectUri) {
      throw new Error('Missing Notion OAuth configuration');
    }

    return `https://api.notion.com/v1/oauth/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code`;
  }

  /**
   * 处理Notion OAuth回调
   * @param code 授权码
   * @returns 返回access_token
   */
  async handleAuthCallback(code: string): Promise<NotionTokenResponse> {
    const clientId = this.configService.get<string>('NOTION_CLIENT_ID');
    const clientSecret = this.configService.get<string>('NOTION_CLIENT_SECRET');
    const redirectUri = this.configService.get<string>('NOTION_REDIRECT_URI');

    if (!clientId || !clientSecret || !redirectUri) {
      throw new Error('Missing Notion OAuth configuration');
    }

    try {
      const response = await axios.post<NotionTokenResponse>(
        'https://api.notion.com/v1/oauth/token',
        {
          grant_type: 'authorization_code',
          code,
          redirect_uri: redirectUri,
        },
        {
          auth: {
            username: clientId,
            password: clientSecret,
          },
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        },
      );

      return response.data;
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Notion OAuth error',
          error.response?.data || error.message,
        );
      } else if (error instanceof Error) {
        this.logger.error('Notion OAuth error', error.message);
      }
      throw new Error('Failed to exchange code for access token');
    }
  }

  /**
   * 将一条记账记录添加到 Notion Database
   * @param accessToken - Notion API 的访问令牌
   * @param databaseId - 目标数据库的 ID
   * @param data - 要添加的记账数据
   */
  async addRecordToDatabase(
    accessToken: string,
    databaseId: string,
    data: RecordData,
  ) {
    // 1. 使用传入的 accessToken 初始化 Notion Client
    const notion = new Client({ auth: accessToken });

    // 2. 调用 Notion SDK 的 pages.create 方法
    // 注意：这里的 '描述', '金额', '分类', '日期' 是你在 Notion Database 中对应的列名
    // 你需要确保这些列名在你的 Notion 数据库中是存在的
    try {
      const response = await notion.pages.create({
        parent: { database_id: databaseId },
        properties: {
          // 将 description 映射为 title 类型的属性
          // Notion API 要求 title 属性的 key 是 'title'，但实际显示在 Notion 中的列名可以自定义
          描述: {
            title: [
              {
                text: {
                  content: data.description,
                },
              },
            ],
          },
          // 将 amount 映射为 number 类型的属性
          金额: {
            number: data.amount,
          },
          // 将 category 映射为 select 或 multi_select 类型的属性
          // 这里我们使用 select，你需要确保 Notion 中有对应的选项
          分类: {
            select: {
              name: data.category,
            },
          },
          // 将 date 映射为 date 类型的属性
          日期: {
            date: {
              start: data.date,
            },
          },
        },
      });
      console.log('Success! Entry added to Notion.', response);
      return response;
    } catch (error) {
      console.error('Error adding record to Notion:', error);
      throw error;
    }
  }
}

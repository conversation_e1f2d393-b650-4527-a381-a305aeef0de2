import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { Client } from '@notionhq/client';
import { PrismaService } from '../../prisma/prisma.service';
import { BillingInfo } from '../ai/ai.service';
import { encryptData, decryptData } from '../../utils/crypto.util';

/**
 * Notion OAuth 返回的Token数据结构
 */
export interface NotionTokenResponse {
  access_token: string;
  token_type: string;
  bot_id: string;
  workspace_name?: string;
  workspace_icon?: string;
  workspace_id?: string;
  owner?: {
    type: string;
    user?: {
      object: string;
      id: string;
    };
  };
}

/**
 * Notion数据库信息
 */
export interface NotionDatabase {
  id: string;
  title: string;
  properties: Record<string, any>;
}

/**
 * 绑定创建DTO
 */
export interface CreateNotionBindingDto {
  userId: string;
  accessToken: string;
  databaseId: string;
  databaseName?: string;
}

@Injectable()
export class NotionService {
  private readonly logger = new Logger(NotionService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly prisma: PrismaService,
  ) {}

  /**
   * 获取Notion OAuth授权URL
   */
  getAuthUrl(state?: string): string {
    const clientId = this.configService.get<string>('NOTION_CLIENT_ID');
    const redirectUri = this.configService.get<string>('NOTION_REDIRECT_URI');

    if (!clientId || !redirectUri) {
      throw new Error('Missing Notion OAuth configuration');
    }

    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
    });

    if (state) {
      params.append('state', state);
    }

    return `https://api.notion.com/v1/oauth/authorize?${params.toString()}`;
  }

  /**
   * 处理Notion OAuth回调
   * @param code 授权码
   * @returns 返回access_token
   */
  async handleAuthCallback(code: string): Promise<NotionTokenResponse> {
    const clientId = this.configService.get<string>('NOTION_CLIENT_ID');
    const clientSecret = this.configService.get<string>('NOTION_CLIENT_SECRET');
    const redirectUri = this.configService.get<string>('NOTION_REDIRECT_URI');

    if (!clientId || !clientSecret || !redirectUri) {
      throw new Error('Missing Notion OAuth configuration');
    }

    try {
      const { data } = await firstValueFrom(
        this.httpService.post<NotionTokenResponse>(
          'https://api.notion.com/v1/oauth/token',
          {
            grant_type: 'authorization_code',
            code,
            redirect_uri: redirectUri,
          },
          {
            auth: {
              username: clientId,
              password: clientSecret,
            },
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
          },
        ),
      );

      return data;
    } catch (error: any) {
      this.logger.error('Notion OAuth error:', error.response?.data || error.message);
      throw new Error('Failed to exchange code for access token');
    }
  }

  /**
   * 获取用户可访问的数据库列表
   */
  async getUserDatabases(accessToken: string): Promise<NotionDatabase[]> {
    const notion = new Client({ auth: accessToken });

    try {
      const response = await notion.search({
        filter: {
          value: 'database',
          property: 'object',
        },
      });

      return response.results.map((db: any) => ({
        id: db.id,
        title: db.title?.[0]?.plain_text || 'Untitled',
        properties: db.properties,
      }));
    } catch (error) {
      this.logger.error('获取Notion数据库列表失败:', error);
      throw new Error('Failed to fetch databases');
    }
  }

  /**
   * 创建绑定关系
   */
  async createBinding(dto: CreateNotionBindingDto): Promise<void> {
    const encryptedToken = encryptData(dto.accessToken);

    await this.prisma.binding.create({
      data: {
        user_id: dto.userId,
        type: 'NOTION',
        access_token: encryptedToken,
        target_id: dto.databaseId,
        target_name: dto.databaseName,
        is_active: true,
      },
    });

    // 将其他Notion绑定设为非活跃
    await this.prisma.binding.updateMany({
      where: {
        user_id: dto.userId,
        type: 'NOTION',
        target_id: { not: dto.databaseId },
      },
      data: {
        is_active: false,
      },
    });
  }

  /**
   * 将一条记账记录添加到 Notion Database
   */
  async addRecordToDatabase(
    accessToken: string,
    databaseId: string,
    data: BillingInfo,
  ): Promise<any> {
    const notion = new Client({ auth: accessToken });

    try {
      // 首先获取数据库结构以了解属性名称
      const database = await notion.databases.retrieve({
        database_id: databaseId,
      });

      // 动态构建属性对象
      const properties: any = {};

      // 查找合适的属性来映射数据
      const dbProperties = (database as any).properties;

      for (const [propName, propConfig] of Object.entries(dbProperties)) {
        const config = propConfig as any;

        // 映射描述到title类型的属性
        if (config.type === 'title' && !properties.title) {
          properties[propName] = {
            title: [
              {
                text: {
                  content: data.description || '记账记录',
                },
              },
            ],
          };
        }

        // 映射金额到number类型的属性
        if (config.type === 'number' && (propName.includes('金额') || propName.includes('amount'))) {
          properties[propName] = {
            number: data.amount,
          };
        }

        // 映射分类到select类型的属性
        if (config.type === 'select' && (propName.includes('分类') || propName.includes('category'))) {
          properties[propName] = {
            select: {
              name: data.category,
            },
          };
        }

        // 映射日期到date类型的属性
        if (config.type === 'date' && (propName.includes('日期') || propName.includes('date'))) {
          properties[propName] = {
            date: {
              start: data.date,
            },
          };
        }
      }

      const response = await notion.pages.create({
        parent: { database_id: databaseId },
        properties,
      });

      this.logger.log('成功添加记录到Notion数据库');
      return response;
    } catch (error) {
      this.logger.error('添加记录到Notion失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的活跃Notion绑定
   */
  async getActiveBinding(userId: string) {
    const binding = await this.prisma.binding.findFirst({
      where: {
        user_id: userId,
        type: 'NOTION',
        is_active: true,
      },
    });

    if (!binding) {
      return null;
    }

    return {
      ...binding,
      access_token: decryptData(binding.access_token),
    };
  }

  /**
   * 同步记账数据到Notion
   */
  async syncBillingData(userId: string, billingInfo: BillingInfo): Promise<void> {
    const binding = await this.getActiveBinding(userId);

    if (!binding) {
      throw new Error('用户未绑定Notion账户');
    }

    await this.addRecordToDatabase(
      binding.access_token,
      binding.target_id,
      billingInfo,
    );
  }
}

import { Controller, Get, Query, Res } from '@nestjs/common';
import { NotionService } from './notion.service';
import { Response } from 'express';
import { NotionTokenResponse } from './notion.service';

@Controller('notion')
export class NotionController {
  constructor(private readonly notionService: NotionService) {}

  /**
   * 获取Notion授权URL并重定向
   */
  @Get('auth')
  getAuthUrl(@Res() res: Response): void {
    const authUrl = this.notionService.getAuthUrl();
    res.redirect(authUrl);
  }

  /**
   * 处理Notion OAuth回调
   * @param code 授权码
   * @returns 返回access_token
   */
  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
  ): Promise<NotionTokenResponse> {
    if (!code) {
      throw new Error('缺少授权码');
    }
    return await this.notionService.handleAuthCallback(code);
  }
}

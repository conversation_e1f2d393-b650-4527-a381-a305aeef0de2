import { Controller, Get, Post, Query, Body, Res, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { NotionService } from './notion.service';

@ApiTags('Notion集成')
@Controller('notion')
export class NotionController {
  constructor(private readonly notionService: NotionService) {}

  @ApiOperation({
    summary: '获取Notion OAuth授权URL',
    description: '生成Notion OAuth授权链接',
  })
  @Get('auth-url')
  getAuthUrl(@Query('userId') userId: string) {
    const authUrl = this.notionService.getAuthUrl(userId);
    return { authUrl };
  }

  @ApiOperation({
    summary: '处理Notion OAuth回调',
    description: '处理Notion OAuth授权回调',
  })
  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
    @Query('state') userId: string,
    @Res() res: Response,
  ) {
    try {
      if (!code) {
        return res.status(400).send('Missing authorization code');
      }

      const tokenResponse = await this.notionService.handleAuthCallback(code);

      if (userId) {
        // 获取用户可访问的数据库
        const databases = await this.notionService.getUserDatabases(tokenResponse.access_token);

        // 重定向到数据库选择页面
        const params = new URLSearchParams({
          access_token: tokenResponse.access_token,
          userId,
          databases: JSON.stringify(databases),
        });

        return res.redirect(`/notion/select-database?${params.toString()}`);
      }

      return res.json(tokenResponse);
    } catch (error) {
      return res.status(500).send('Authorization failed');
    }
  }

  @ApiOperation({
    summary: '获取用户数据库列表',
    description: '获取用户可访问的Notion数据库列表',
  })
  @Post('databases')
  async getDatabases(@Body() body: { accessToken: string }) {
    return await this.notionService.getUserDatabases(body.accessToken);
  }

  @ApiOperation({
    summary: '创建绑定',
    description: '创建用户与Notion数据库的绑定关系',
  })
  @Post('bind')
  async createBinding(@Body() body: {
    userId: string;
    accessToken: string;
    databaseId: string;
    databaseName?: string;
  }) {
    await this.notionService.createBinding(body);
    return { success: true };
  }

  @ApiOperation({
    summary: '获取用户绑定信息',
    description: '获取用户的活跃Notion绑定信息',
  })
  @Get('binding/:userId')
  async getBinding(@Param('userId') userId: string) {
    const binding = await this.notionService.getActiveBinding(userId);
    if (!binding) {
      return { bound: false };
    }

    return {
      bound: true,
      databaseId: binding.target_id,
      databaseName: binding.target_name,
    };
  }
}

import { <PERSON>, Get, Post, Query, Body, Res, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { NotionService } from './notion.service';

@ApiTags('Notion集成')
@Controller('notion')
export class NotionController {
  private readonly logger = new Logger(NotionController.name);

  constructor(private readonly notionService: NotionService) {}

  @ApiOperation({
    summary: '获取Notion OAuth授权URL',
    description: '生成Notion OAuth授权链接',
  })
  @Get('auth-url')
  getAuthUrl(@Query('userId') userId: string) {
    const authUrl = this.notionService.getAuthUrl(userId);
    return { authUrl };
  }

  @ApiOperation({
    summary: '处理Notion OAuth回调',
    description: '处理Notion OAuth授权回调',
  })
  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
    @Query('state') userId: string,
    @Query('error') error: string,
    @Res() res: Response,
  ) {
    try {
      // 检查是否有错误
      if (error) {
        this.logger.error(`Notion OAuth错误: ${error}`);
        return res.status(400).send(`授权失败: ${error}`);
      }

      if (!code) {
        return res.status(400).send('缺少授权码');
      }

      // 交换访问令牌
      const tokenResponse = await this.notionService.handleAuthCallback(code);

      if (userId) {
        try {
          // 获取用户可访问的数据库
          const databases = await this.notionService.getUserDatabases(tokenResponse.access_token);

          if (databases.length === 0) {
            return res.send(`
              <html>
                <body>
                  <h2>未找到可用的数据库</h2>
                  <p>请确保您的Notion工作区中有数据库，并且已授权访问。</p>
                  <p>您可以创建一个新的数据库，然后重新授权。</p>
                </body>
              </html>
            `);
          }

          // 生成数据库选择页面
          const databaseOptions = databases.map(db =>
            `<option value="${db.id}">${db.title}</option>`
          ).join('');

          const html = `
            <html>
              <head>
                <title>选择Notion数据库</title>
                <style>
                  body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
                  .form-group { margin-bottom: 15px; }
                  label { display: block; margin-bottom: 5px; font-weight: bold; }
                  select, button { padding: 10px; font-size: 16px; }
                  select { width: 100%; }
                  button { background: #0070f3; color: white; border: none; cursor: pointer; }
                  button:hover { background: #0051cc; }
                </style>
              </head>
              <body>
                <h2>选择要同步的Notion数据库</h2>
                <form action="/notion/complete-binding" method="post">
                  <input type="hidden" name="userId" value="${userId}" />
                  <input type="hidden" name="accessToken" value="${tokenResponse.access_token}" />

                  <div class="form-group">
                    <label for="databaseId">选择数据库:</label>
                    <select name="databaseId" id="databaseId" required>
                      <option value="">请选择数据库</option>
                      ${databaseOptions}
                    </select>
                  </div>

                  <button type="submit">完成绑定</button>
                </form>

                <p><small>选择数据库后，您的记账数据将自动同步到该数据库中。</small></p>
              </body>
            </html>
          `;

          return res.send(html);
        } catch (dbError) {
          this.logger.error('获取数据库列表失败:', dbError);
          return res.status(500).send('获取数据库列表失败，请重试');
        }
      }

      return res.json(tokenResponse);
    } catch (error) {
      this.logger.error('处理Notion回调失败:', error);
      return res.status(500).send('授权处理失败，请重试');
    }
  }

  @ApiOperation({
    summary: '获取用户数据库列表',
    description: '获取用户可访问的Notion数据库列表',
  })
  @Post('databases')
  async getDatabases(@Body() body: { accessToken: string }) {
    return await this.notionService.getUserDatabases(body.accessToken);
  }

  @ApiOperation({
    summary: '完成绑定',
    description: '完成用户与Notion数据库的绑定',
  })
  @Post('complete-binding')
  async completeBinding(
    @Body() body: {
      userId: string;
      accessToken: string;
      databaseId: string;
    },
    @Res() res: Response,
  ) {
    try {
      // 获取数据库信息
      const databases = await this.notionService.getUserDatabases(body.accessToken);
      const selectedDatabase = databases.find(db => db.id === body.databaseId);

      if (!selectedDatabase) {
        return res.status(400).send('选择的数据库不存在');
      }

      // 创建绑定
      await this.notionService.createBinding({
        userId: body.userId,
        accessToken: body.accessToken,
        databaseId: body.databaseId,
        databaseName: selectedDatabase.title,
      });

      // 返回成功页面
      const html = `
        <html>
          <head>
            <title>绑定成功</title>
            <style>
              body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center; }
              .success { color: #28a745; font-size: 24px; margin-bottom: 20px; }
              .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
            </style>
          </head>
          <body>
            <div class="success">✅ Notion绑定成功！</div>
            <div class="info">
              <p><strong>已绑定数据库:</strong> ${selectedDatabase.title}</p>
              <p>现在您可以在企业微信中发送记账信息，数据将自动同步到此数据库。</p>
            </div>
            <p>您可以关闭此页面，返回企业微信开始记账。</p>
          </body>
        </html>
      `;

      return res.send(html);
    } catch (error) {
      this.logger.error('完成绑定失败:', error);
      return res.status(500).send('绑定失败，请重试');
    }
  }

  @ApiOperation({
    summary: '创建绑定',
    description: '创建用户与Notion数据库的绑定关系',
  })
  @Post('bind')
  async createBinding(@Body() body: {
    userId: string;
    accessToken: string;
    databaseId: string;
    databaseName?: string;
  }) {
    await this.notionService.createBinding(body);
    return { success: true };
  }

  @ApiOperation({
    summary: '获取用户绑定信息',
    description: '获取用户的活跃Notion绑定信息',
  })
  @Get('binding/:userId')
  async getBinding(@Param('userId') userId: string) {
    const binding = await this.notionService.getActiveBinding(userId);
    if (!binding) {
      return { bound: false };
    }

    return {
      bound: true,
      databaseId: binding.target_id,
      databaseName: binding.target_name,
    };
  }
}

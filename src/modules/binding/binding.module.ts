import { <PERSON>du<PERSON> } from '@nestjs/common';
import { BindingController } from './binding.controller';
import { BindingService } from './binding.service';
import { WecomModule } from './wecom.module';
import { WecomController } from './wecom.controller';
import { WecomService } from './wecom.service';
import { AiModule } from './ai/ai.module';
import { NotionModule } from './notion/notion.module';
import { FeishuModule } from './feishu/feishu.module';

@Module({
  controllers: [BindingController, WecomController],
  providers: [BindingService, WecomService],
  imports: [WecomModule, AiModule, NotionModule, FeishuModule]
})
export class BindingModule {}

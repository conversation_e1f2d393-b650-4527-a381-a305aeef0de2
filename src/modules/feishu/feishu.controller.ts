import { Controller, Get, Res, Query } from '@nestjs/common';
import { Response } from 'express';
import { FeishuService } from './feishu.service';
import { BindingService } from '../binding/binding.service';

@Controller('feishu')
export class FeishuController {
  constructor(
    private readonly feishuService: FeishuService,
    private readonly bindingService: BindingService,
  ) {}

  /**
   * 获取飞书OAuth授权URL并重定向
   */
  @Get('auth')
  getAuthUrl(@Res() res: Response): void {
    const authUrl = this.feishuService.getAuthUrl();
    res.redirect(authUrl);
  }

  /**
   * 处理飞书OAuth回调
   * @param code 授权码
   */
  @Get('callback')
  async handleAuthCallback(@Query('code') code: string) {
    const tokenInfo = await this.feishuService.handleAuthCallback(code);

    // TODO: 替换为从会话或JWT中获取的真实用户ID
    const userId = 'clx15nruq0000o8t5ay3e5k1j';

    await this.bindingService.createFeishuBinding(
      userId,
      tokenInfo.accessToken,
      tokenInfo.refreshToken,
    );

    return { message: '飞书绑定成功' };
  }
}

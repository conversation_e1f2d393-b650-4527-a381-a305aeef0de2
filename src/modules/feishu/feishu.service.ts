import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from '@larksuiteoapi/api';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

interface BillingData {
  amount: number;
  category: string;
  date: string;
  description: string;
}

export interface FeishuAuthToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

interface FeishuTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  [key: string]: any;
}

@Injectable()
export class FeishuService {
  private readonly logger = new Logger(FeishuService.name);
  private client: Client;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.initClient();
  }

  private initClient() {
    try {
      this.client = new Client({
        appId: this.configService.get<string>('FEISHU_APP_ID')!,
        appSecret: this.configService.get<string>('FEISHU_APP_SECRET')!,
        domain: 'feishu',
      });
    } catch (error) {
      this.logger.error('初始化飞书客户端失败', error);
      throw error;
    }
  }

  /**
   * 添加记账记录到飞书文档
   * @param documentId 文档ID
   * @param data 记账数据
   */
  async addRecordToDocument(
    documentId: string,
    data: BillingData,
  ): Promise<void> {
    const content = `| ${data.date} | ${data.description} | ¥${data.amount} | ${data.category} |\n`;
    try {
      await this.client.documents.appendContent({
        document_id: documentId,
        content: content,
        content_type: 'markdown',
      });
    } catch (error) {
      this.logger.error('添加记账记录失败', error);
      throw error;
    }
  }
  /**
   * 获取飞书OAuth授权URL
   */
  getAuthUrl(): string {
    const clientId = this.configService.get<string>('FEISHU_APP_ID')!;
    const redirectUri = this.configService.get<string>('FEISHU_REDIRECT_URI')!;
    return `https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=${encodeURIComponent(redirectUri)}&app_id=${clientId}&state=STATE`;
  }

  /**
   * 处理飞书OAuth回调
   * @param code 授权码
   */
  async handleAuthCallback(code: string): Promise<FeishuAuthToken> {
    const clientId = this.configService.get<string>('FEISHU_APP_ID')!;
    const clientSecret = this.configService.get<string>('FEISHU_APP_SECRET')!;
    const redirectUri = this.configService.get<string>('FEISHU_REDIRECT_URI')!;

    try {
      const response = await firstValueFrom(
        this.httpService.post<FeishuTokenResponse>(
          'https://open.feishu.cn/open-apis/authen/v1/access_token',
          {
            grant_type: 'authorization_code',
            code,
            client_id: clientId,
            client_secret: clientSecret,
            redirect_uri: redirectUri,
          },
        ),
      );

      return {
        accessToken: response.data.access_token,
        refreshToken: response.data.refresh_token,
        expiresIn: response.data.expires_in,
      };
    } catch (error) {
      this.logger.error('获取飞书access token失败', error);
      throw error;
    }
  }

  /**
   * 添加内容到飞书文档 (保留原方法)
   * @param documentId 文档ID
   * @param content 要添加的内容(Markdown格式)
   */
  async appendToDocument(documentId: string, content: string): Promise<any> {
    try {
      const result = await this.client.documents.appendContent({
        document_id: documentId,
        content: content,
        content_type: 'markdown',
      });
      return result;
    } catch (error) {
      this.logger.error('添加记录到飞书文档失败', error);
      throw error;
    }
  }
}

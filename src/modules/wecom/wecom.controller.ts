import { Controller, Post, Body, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';
import { WecomMessageDto } from './dto/wecom-message.dto';
import { UserService } from '../user/user.service';

@ApiTags('企业微信接口')
@Controller('wecom')
export class WecomController {
  private readonly logger = new Logger(WecomController.name);

  constructor(private readonly userService: UserService) {}

  @ApiOperation({
    summary: '处理企业微信回调',
    description: '接收并处理来自企业微信的消息和事件回调',
  })
  @ApiBody({ type: WecomMessageDto })
  @ApiResponse({
    status: 200,
    description: '处理成功',
  })
  @ApiResponse({
    status: 400,
    description: '无效请求',
  })
  @Post('callback')
  async handleMessage(@Body() message: WecomMessageDto) {
    this.logger.log(`收到企业微信消息: ${JSON.stringify(message)}`);

    // 处理文本消息
    if (message.MsgType === 'text' && message.Content) {
      return this.handleTextMessage(message);
    }

    // 处理事件消息
    if (message.MsgType === 'event' && message.Event) {
      return this.handleEventMessage(message);
    }

    return 'success';
  }

  private async handleTextMessage(message: WecomMessageDto) {
    const userId = message.FromUserName;
    const content = message.Content?.trim();

    // 解析指令
    if (content === '绑定Notion') {
      await this.userService.setUserState(userId, 'awaiting_binding_notion');
      return '请提供您的Notion数据库ID';
    }

    if (content === '绑定飞书') {
      await this.userService.setUserState(userId, 'awaiting_binding_feishu');
      return '请提供您的飞书多维表格ID';
    }

    return '未知指令，请输入"绑定Notion"或"绑定飞书"';
  }

  private handleEventMessage(message: WecomMessageDto) {
    this.logger.log(`收到企业微信事件: ${message.Event}`);
    return 'success';
  }
}

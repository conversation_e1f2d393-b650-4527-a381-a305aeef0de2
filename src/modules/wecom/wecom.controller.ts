import { Controller, Post, Body, Logger, Get, Query, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { WecomMessageDto } from './dto/wecom-message.dto';
import { WecomService } from './wecom.service';
import { UserService } from '../user/user.service';

@ApiTags('企业微信接口')
@Controller('wecom')
export class WecomController {
  private readonly logger = new Logger(WecomController.name);

  constructor(
    private readonly wecomService: WecomService,
    private readonly userService: UserService,
  ) {}

  @ApiOperation({
    summary: '验证企业微信回调URL',
    description: '用于验证企业微信回调URL的有效性',
  })
  @Get('callback')
  async verifyUrl(
    @Query('msg_signature') msgSignature: string,
    @Query('timestamp') timestamp: string,
    @Query('nonce') nonce: string,
    @Query('echostr') echostr: string,
    @Res() res: Response,
  ) {
    try {
      const result = this.wecomService.verifyUrl(msgSignature, timestamp, nonce, echostr);
      res.send(result);
    } catch (error) {
      this.logger.error('URL验证失败:', error);
      res.status(400).send('验证失败');
    }
  }

  @ApiOperation({
    summary: '处理企业微信回调',
    description: '接收并处理来自企业微信的消息和事件回调',
  })
  @ApiResponse({
    status: 200,
    description: '处理成功',
  })
  @ApiResponse({
    status: 400,
    description: '无效请求',
  })
  @Post('callback')
  async handleMessage(
    @Query('msg_signature') msgSignature: string,
    @Query('timestamp') timestamp: string,
    @Query('nonce') nonce: string,
    @Body() encryptedData: string,
  ) {
    try {
      await this.wecomService.handleIncomingMessage(
        msgSignature,
        timestamp,
        nonce,
        encryptedData,
      );
      return 'success';
    } catch (error) {
      this.logger.error('处理消息失败:', error);
      return 'error';
    }
  }

  @ApiOperation({
    summary: '发送消息给用户',
    description: '主动发送消息给指定用户',
  })
  @Post('send-message')
  async sendMessage(
    @Body() body: { userId: string; message: string },
  ) {
    try {
      await this.wecomService.sendMessage(body.userId, body.message);
      return { success: true };
    } catch (error) {
      this.logger.error('发送消息失败:', error);
      return { success: false, error: error.message };
    }
  }
}

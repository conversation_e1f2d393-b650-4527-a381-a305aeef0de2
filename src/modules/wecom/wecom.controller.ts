import {
  <PERSON>,
  Post,
  Body,
  Logger,
  Get,
  Query,
  Res,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { WecomService } from './wecom.service';

@ApiTags('企业微信客服消息')
@Controller('wecom')
export class WecomController {
  private readonly logger = new Logger(WecomController.name);

  constructor(private readonly wecomService: WecomService) {}

  @ApiOperation({
    summary: '验证企业微信回调URL',
    description: '用于配置企业微信客服消息回调地址时的URL验证',
  })
  @ApiResponse({ status: 200, description: '返回解密后的echostr' })
  @Get('callback')
  async verifyUrl(
    @Query('msg_signature') msgSignature: string,
    @Query('timestamp') timestamp: string,
    @Query('nonce') nonce: string,
    @Query('echostr') echostr: string,
    @Res() res: Response,
  ) {
    try {
      this.logger.log('收到URL验证请求', { msgSignature, timestamp, nonce });

      const result = await this.wecomService.verifyUrl(
        msgSignature,
        timestamp,
        nonce,
        echostr,
      );

      this.logger.log('URL验证成功');
      return res.send(result);
    } catch (error) {
      this.logger.error('URL验证失败:', error);
      return res.status(400).send('verification failed');
    }
  }

  @ApiOperation({
    summary: '接收企业微信客服消息事件',
    description: '接收企业微信推送的客服消息事件，解密后获取token和cursor，然后拉取增量消息进行处理',
  })
  @ApiResponse({ status: 200, description: '返回success表示处理成功' })
  @Post('callback')
  async receiveMessageEvent(
    @Query('msg_signature') msgSignature: string,
    @Query('timestamp') timestamp: string,
    @Query('nonce') nonce: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      this.logger.log('收到客服消息事件回调', { msgSignature, timestamp, nonce });

      // 获取原始XML数据
      let xmlData: string;
      if (typeof req.body === 'string') {
        xmlData = req.body;
      } else {
        xmlData = JSON.stringify(req.body);
      }

      // 处理客服消息事件
      await this.wecomService.handleKfMessageEvent(
        msgSignature,
        timestamp,
        nonce,
        xmlData,
      );

      this.logger.log('客服消息事件处理成功');
      return res.send('success');
    } catch (error) {
      this.logger.error('处理客服消息事件失败:', error);
      return res.status(500).send('error');
    }
  }
}
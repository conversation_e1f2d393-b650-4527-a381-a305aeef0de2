import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { getSignature, decrypt, encrypt } from '@wecom/crypto';
import { parseStringPromise } from 'xml2js';
import { firstValueFrom } from 'rxjs';
import { UserService } from '../user/user.service';
import { PrismaService } from '../../prisma/prisma.service';

interface KfMessageEvent {
  Token: string;
  Cursor: string;
}

interface KfMessage {
  msgid: string;
  open_kfid: string;
  external_userid: string;
  send_time: number;
  origin: number;
  msgtype: string;
  text?: {
    content: string;
  };
  image?: {
    media_id: string;
  };
  voice?: {
    media_id: string;
  };
  file?: {
    media_id: string;
  };
}

@Injectable()
export class WecomService {
  private readonly logger = new Logger(WecomService.name);
  private readonly token: string;
  private readonly encodingAESKey: string;
  private readonly corpId: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly prisma: PrismaService,
    private readonly httpService: HttpService,
  ) {
    // 从环境变量中获取企业微信配置
    this.token = this.configService.get<string>('WECOM_TOKEN') || '';
    this.encodingAESKey = this.configService.get<string>('WECOM_ENCODING_AES_KEY') || '';
    this.corpId = this.configService.get<string>('WECOM_CORP_ID') || '';

    if (!this.token || !this.encodingAESKey || !this.corpId) {
      throw new Error('企业微信配置不完整，请检查环境变量');
    }

    this.logger.log('企业微信服务初始化完成');
  }

  /**
   * 验证URL
   */
  async verifyUrl(
    msgSignature: string,
    timestamp: string,
    nonce: string,
    echostr: string,
  ): Promise<string> {
    try {
      // 验证签名
      const signature = getSignature(this.token, timestamp, nonce, echostr);
      if (signature !== msgSignature) {
        throw new Error('签名验证失败');
      }

      // 解密echostr
      const { message } = decrypt(this.encodingAESKey, echostr);
      this.logger.log('URL验证成功');
      return message;
    } catch (error) {
      this.logger.error('URL验证失败:', error);
      throw error;
    }
  }

  /**
   * 处理客服消息事件
   */
  async handleKfMessageEvent(
    msgSignature: string,
    timestamp: string,
    nonce: string,
    xmlData: string,
  ): Promise<void> {
    try {
      this.logger.log('开始处理客服消息事件');

      // 1. 验证签名并解密XML数据
      const signature = getSignature(this.token, timestamp, nonce, xmlData);
      if (signature !== msgSignature) {
        throw new Error('消息签名验证失败');
      }

      const { message } = decrypt(this.encodingAESKey, xmlData);
      this.logger.log('消息解密成功');

      // 2. 解析XML获取事件信息
      const parsedXml = await parseStringPromise(message, {
        explicitArray: false,
        tagNameProcessors: [(name) => name],
      });

      const eventData = parsedXml.xml;
      this.logger.log('解析事件数据:', eventData);

      // 3. 检查是否是客服消息事件
      if (eventData.InfoType === 'kf_msg_or_event') {
        const kfEvent: KfMessageEvent = {
          Token: eventData.Token,
          Cursor: eventData.Cursor,
        };

        this.logger.log('收到客服消息事件:', kfEvent);

        // 4. 使用Token和Cursor拉取增量消息
        await this.pullKfMessages(kfEvent.Token, kfEvent.Cursor);
      } else {
        this.logger.log('非客服消息事件，忽略处理');
      }
    } catch (error) {
      this.logger.error('处理客服消息事件失败:', error);
      throw error;
    }
  }

  /**
   * 拉取客服增量消息
   */
  private async pullKfMessages(token: string, cursor: string): Promise<void> {
    try {
      this.logger.log('开始拉取客服增量消息', { token, cursor });

      const accessToken = await this.getAccessToken();
      const apiUrl = `https://qyapi.weixin.qq.com/cgi-bin/kf/sync_msg?access_token=${accessToken}`;

      const requestData = {
        cursor,
        token,
        limit: 1000, // 每次最多拉取1000条消息
      };

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, requestData),
      );

      if (response.data.errcode !== 0) {
        throw new Error(`拉取消息失败: ${response.data.errmsg}`);
      }

      const { msg_list, next_cursor } = response.data;
      this.logger.log(`成功拉取到 ${msg_list?.length || 0} 条消息`);

      // 处理每条消息
      if (msg_list && msg_list.length > 0) {
        for (const message of msg_list) {
          await this.processKfMessage(message);
        }
      }

      // 如果还有更多消息，继续拉取
      if (next_cursor) {
        await this.pullKfMessages(token, next_cursor);
      }
    } catch (error) {
      this.logger.error('拉取客服消息失败:', error);
      throw error;
    }
  }

  /**
   * 处理单条客服消息
   */
  private async processKfMessage(message: KfMessage): Promise<void> {
    try {
      this.logger.log('处理客服消息:', message);

      // 只处理用户发送的消息（origin: 3表示用户发送）
      if (message.origin !== 3) {
        this.logger.log('非用户消息，跳过处理');
        return;
      }

      const userId = message.external_userid;
      const kfId = message.open_kfid;

      // 确保用户存在
      await this.ensureUserExists(userId);

      // 根据消息类型处理
      let messageContent = '';
      const messageType = message.msgtype;

      switch (messageType) {
        case 'text':
          messageContent = message.text?.content || '';
          break;
        case 'image':
          messageContent = `[图片消息: ${message.image?.media_id}]`;
          // TODO: 下载并处理图片
          break;
        case 'voice':
          messageContent = `[语音消息: ${message.voice?.media_id}]`;
          // TODO: 下载并处理语音
          break;
        case 'file':
          messageContent = `[文件消息: ${message.file?.media_id}]`;
          // TODO: 下载并处理文件
          break;
        default:
          this.logger.log(`不支持的消息类型: ${messageType}`);
          return;
      }

      this.logger.log('消息内容:', { userId, messageType, messageContent });

      // 发送处理中的提示消息
      await this.sendKfMessage(kfId, userId, '收到您的消息，正在处理中...');

      // TODO: 将消息发送到消息队列进行AI分析
      // await this.sendToMessageQueue(userId, messageType, messageContent);

      // 暂时发送一个简单的回复
      await this.sendKfMessage(kfId, userId, '您的消息已收到，记账功能正在开发中，敬请期待！');

    } catch (error) {
      this.logger.error('处理客服消息失败:', error);
    }
  }

  /**
   * 发送客服消息
   */
  async sendKfMessage(kfId: string, userId: string, content: string): Promise<void> {
    try {
      const accessToken = await this.getAccessToken();
      const apiUrl = `https://qyapi.weixin.qq.com/cgi-bin/kf/send_msg?access_token=${accessToken}`;

      const messageData = {
        touser: userId,
        open_kfid: kfId,
        msgtype: 'text',
        text: {
          content,
        },
      };

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, messageData),
      );

      if (response.data.errcode !== 0) {
        throw new Error(`发送消息失败: ${response.data.errmsg}`);
      }

      this.logger.log('客服消息发送成功');
    } catch (error) {
      this.logger.error('发送客服消息失败:', error);
      throw error;
    }
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<string> {
    try {
      const corpId = this.configService.get<string>('WECOM_CORP_ID');
      const secret = this.configService.get<string>('WECOM_SECRET');

      const url = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpId}&corpsecret=${secret}`;

      const response = await firstValueFrom(this.httpService.get(url));

      if (response.data.errcode !== 0) {
        throw new Error(`获取访问令牌失败: ${response.data.errmsg}`);
      }

      return response.data.access_token;
    } catch (error) {
      this.logger.error('获取访问令牌失败:', error);
      throw error;
    }
  }

  /**
   * 确保用户存在
   */
  private async ensureUserExists(userId: string): Promise<void> {
    try {
      const existingUser = await this.userService.findOne(userId);
      if (!existingUser) {
        await this.userService.create({
          wecomUserId: userId,
          name: `用户_${userId.substring(0, 8)}`,
        });
        this.logger.log(`创建新用户: ${userId}`);
      }
    } catch (error) {
      this.logger.error('确保用户存在失败:', error);
    }
  }
}
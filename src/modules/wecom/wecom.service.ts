/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import WeComCrypto from '@wecom/crypto';
import { parseStringPromise } from 'xml2js';
import { firstValueFrom } from 'rxjs';
import { UserService } from '../user/user.service';
import { WecomMessageDto } from './dto/wecom-message.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { MessageType, MessageContent } from '../ai/ai.service';

@Injectable()
export class WecomService {
  private readonly logger = new Logger(WecomService.name);
  private readonly crypto: any;

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly prisma: PrismaService,
    private readonly httpService: HttpService,
    @Inject(forwardRef(() => import('../billing/billing.service').then(m => m.BillingService)))
    private readonly billingService: any,
  ) {
    // 从环境变量中获取企业微信配置
    const token = this.configService.get<string>('WECOM_TOKEN');
    const encodingAESKey = this.configService.get<string>(
      'WECOM_ENCODING_AES_KEY',
    );
    const corpId = this.configService.get<string>('WECOM_CORP_ID');

    if (!token || !encodingAESKey || !corpId) {
      throw new Error('企业微信配置不完整，请检查 .env 文件');
    }

    // 初始化加解密实例
    this.crypto = new (WeComCrypto as any)(token, encodingAESKey, corpId);
  }

  /**
   * @method verifyUrl
   * @description 验证企业微信回调 URL
   * @param {string} msg_signature - 消息签名
   * @param {string} timestamp - 时间戳
   * @param {string} nonce - 随机数
   * @param {string} echostr - 加密的随机字符串
   * @returns {string} 解密后的字符串
   */
  verifyUrl(
    msg_signature: string,
    timestamp: string,
    nonce: string,
    echostr: string,
  ): string {
    try {
      const { message } = this.crypto.decrypt(echostr, {
        msg_signature,
        timestamp,
        nonce,
      });
      return message;
    } catch (e) {
      this.logger.error('URL 验证失败', e.stack);
      throw new Error('URL 验证失败');
    }
  }

  /**
   * @method handleIncomingMessage
   * @description 处理接收到的企业微信消息
   * @param {string} msg_signature - 消息签名
   * @param {string} timestamp - 时间戳
   * @param {string} nonce - 随机数
   * @param {string} xmlData - 加密的 XML 消息体
   */
  async handleIncomingMessage(
    msg_signature: string,
    timestamp: string,
    nonce: string,
    xmlData: string,
  ): Promise<void> {
    try {
      // 1. 解密消息
      const { message } = this.crypto.decrypt(xmlData, {
        msg_signature,
        timestamp,
        nonce,
      });

      // 2. 解析 XML
      const parsedXml = await parseStringPromise(message, {
        explicitArray: false,
        tagNameProcessors: [(name) => name], // 保持原始标签名
      });

      const rawMessage = (parsedXml as any).xml;
      const messageDto: WecomMessageDto = {
        ToUserName: rawMessage.ToUserName,
        FromUserName: rawMessage.FromUserName,
        CreateTime: parseInt(rawMessage.CreateTime, 10),
        MsgType: rawMessage.MsgType,
        Content: rawMessage.Content,
        MsgId: parseInt(rawMessage.MsgId, 10),
        AgentID: parseInt(rawMessage.AgentID, 10),
        Event: rawMessage.Event,
      };
      this.logger.log('接收并解密消息成功:', messageDto);

      // 3. 处理业务逻辑
      const wecomUserId = messageDto.FromUserName;
      if (!wecomUserId) {
        this.logger.warn('消息中缺少 FromUserName', messageDto);
        return;
      }

      // 4. 检查用户是否存在，不存在则创建
      let user = await this.userService.findOne(wecomUserId);
      if (!user) {
        this.logger.log(`用户 ${wecomUserId} 不存在，将创建新用户`);
        user = await this.userService.create({
          wecomUserId: wecomUserId,
          name: wecomUserId, // 暂时使用 wecomUserId 作为 name
        });
        this.logger.log(`用户 ${wecomUserId} 创建成功`, user);
      } else {
        this.logger.log(`用户 ${wecomUserId} 已存在`, user);
      }

      // 5. 检查用户使用量
      const hasValidSubscription = await this.checkUserSubscription(user.id);
      if (!hasValidSubscription) {
        await this.sendMessage(user.id, '您的使用额度已用完，请升级订阅以继续使用。');
        return;
      }

      // 6. 处理不同类型的消息
      await this.processMessage(messageDto, user.id);
    } catch (error) {
      this.logger.error('处理企业微信消息时发生错误', error.stack);
      throw error; // 向上抛出异常，让 Controller 捕获
    }
  }
  /**
   * 检查用户订阅状态
   */
  private async checkUserSubscription(userId: string): Promise<boolean> {
    // 检查是否有有效订阅
    const subscription = await this.prisma.subscription.findFirst({
      where: {
        user_id: userId,
        status: 'active',
        end_date: { gte: new Date() },
      },
      orderBy: { end_date: 'desc' },
    });

    if (subscription) {
      const user = await this.userService.findOne(userId);
      return user && user.usageCount < subscription.limit;
    }

    // 检查免费额度
    const freeLimit = this.configService.get<number>('FREE_USER_MONTHLY_LIMIT', 30);
    const user = await this.userService.findOne(userId);
    return user && user.usageCount < freeLimit;
  }

  /**
   * 处理消息
   */
  private async processMessage(messageDto: WecomMessageDto, userId: string): Promise<void> {
    try {
      // 检查是否是指令
      if (messageDto.MsgType === 'text' && messageDto.Content) {
        const commandResponse = await this.billingService.handleBindingCommand(userId, messageDto.Content);
        if (commandResponse) {
          await this.sendMessage(userId, commandResponse);
          return;
        }
      }

      let messageContent: MessageContent;

      // 根据消息类型构建MessageContent
      switch (messageDto.MsgType) {
        case 'text':
          if (!messageDto.Content) {
            await this.sendMessage(userId, '收到空的文本消息');
            return;
          }
          messageContent = {
            type: MessageType.TEXT,
            content: messageDto.Content,
          };
          break;

        case 'image':
          // TODO: 处理图片消息，需要下载图片
          await this.sendMessage(userId, '图片消息处理功能开发中...');
          return;

        case 'voice':
          // TODO: 处理语音消息
          await this.sendMessage(userId, '语音消息处理功能开发中...');
          return;

        case 'file':
          // TODO: 处理文件消息
          await this.sendMessage(userId, '文件消息处理功能开发中...');
          return;

        default:
          this.logger.warn(`不支持的消息类型: ${messageDto.MsgType}`);
          return;
      }

      // 使用BillingService处理记账
      const result = await this.billingService.processBilling(userId, messageContent);

      if (!result.success) {
        this.logger.warn(`记账处理失败: ${result.message}`, result.error);
      }

    } catch (error) {
      this.logger.error('处理消息失败:', error);
      await this.sendMessage(userId, '处理消息时发生错误，请稍后重试');
    }
  }



  /**
   * 发送消息给用户
   */
  async sendMessage(userId: string, message: string): Promise<void> {
    try {
      const accessToken = await this.getAccessToken();
      const apiUrl = `https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=${accessToken}`;

      const requestBody = {
        touser: userId,
        msgtype: 'text',
        agentid: this.configService.get<string>('WECOM_AGENT_ID'),
        text: {
          content: message,
        },
      };

      await firstValueFrom(
        this.httpService.post(apiUrl, requestBody, {
          headers: { 'Content-Type': 'application/json' },
        }),
      );

      this.logger.log(`消息发送成功: ${userId}`);
    } catch (error) {
      this.logger.error('发送消息失败:', error);
    }
  }

  /**
   * 获取企业微信访问令牌
   */
  private async getAccessToken(): Promise<string> {
    const corpId = this.configService.get<string>('WECOM_CORP_ID');
    const secret = this.configService.get<string>('WECOM_SECRET');

    const url = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpId}&corpsecret=${secret}`;

    const { data } = await firstValueFrom(
      this.httpService.get(url),
    );

    if (data.errcode !== 0) {
      throw new Error(`获取访问令牌失败: ${data.errmsg}`);
    }

    return data.access_token;
  }

  /**
   * 下载媒体文件
   */
  async getMedia(mediaId: string): Promise<Buffer> {
    try {
      const accessToken = await this.getAccessToken();
      const url = `https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=${accessToken}&media_id=${mediaId}`;

      const { data } = await firstValueFrom(
        this.httpService.get(url, { responseType: 'arraybuffer' }),
      );

      return Buffer.from(data);
    } catch (error) {
      this.logger.error('下载媒体文件失败:', error);
      throw error;
    }
  }
}

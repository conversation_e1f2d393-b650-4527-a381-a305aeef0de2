/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import WeComCrypto from '@wecom/crypto';
import { parseStringPromise } from 'xml2js';
import { UserService } from '../user/user.service';
import { WecomMessageDto } from './dto/wecom-message.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { FeishuService } from '../feishu/feishu.service';

@Injectable()
export class WecomService {
  private readonly logger = new Logger(WecomService.name);
  private readonly crypto: any;

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly prisma: PrismaService, // 注入PrismaService
    private readonly feishuService: FeishuService, // 注入FeishuService
  ) {
    // 从环境变量中获取企业微信配置
    const token = this.configService.get<string>('WECOM_TOKEN');
    const encodingAESKey = this.configService.get<string>(
      'WECOM_ENCODING_AES_KEY',
    );
    const corpId = this.configService.get<string>('WECOM_CORP_ID');

    if (!token || !encodingAESKey || !corpId) {
      throw new Error('企业微信配置不完整，请检查 .env 文件');
    }

    // 初始化加解密实例
    this.crypto = new (WeComCrypto as any)(token, encodingAESKey, corpId);
  }

  /**
   * @method verifyUrl
   * @description 验证企业微信回调 URL
   * @param {string} msg_signature - 消息签名
   * @param {string} timestamp - 时间戳
   * @param {string} nonce - 随机数
   * @param {string} echostr - 加密的随机字符串
   * @returns {string} 解密后的字符串
   */
  verifyUrl(
    msg_signature: string,
    timestamp: string,
    nonce: string,
    echostr: string,
  ): string {
    try {
      const { message } = this.crypto.decrypt(echostr, {
        msg_signature,
        timestamp,
        nonce,
      });
      return message;
    } catch (e) {
      this.logger.error('URL 验证失败', e.stack);
      throw new Error('URL 验证失败');
    }
  }

  /**
   * @method handleIncomingMessage
   * @description 处理接收到的企业微信消息
   * @param {string} msg_signature - 消息签名
   * @param {string} timestamp - 时间戳
   * @param {string} nonce - 随机数
   * @param {string} xmlData - 加密的 XML 消息体
   */
  async handleIncomingMessage(
    msg_signature: string,
    timestamp: string,
    nonce: string,
    xmlData: string,
  ): Promise<void> {
    try {
      // 1. 解密消息
      const { message } = this.crypto.decrypt(xmlData, {
        msg_signature,
        timestamp,
        nonce,
      });

      // 2. 解析 XML
      const parsedXml = await parseStringPromise(message, {
        explicitArray: false,
        tagNameProcessors: [(name) => name], // 保持原始标签名
      });

      const rawMessage = (parsedXml as any).xml;
      const messageDto: WecomMessageDto = {
        ToUserName: rawMessage.ToUserName,
        FromUserName: rawMessage.FromUserName,
        CreateTime: parseInt(rawMessage.CreateTime, 10),
        MsgType: rawMessage.MsgType,
        Content: rawMessage.Content,
        MsgId: parseInt(rawMessage.MsgId, 10),
        AgentID: parseInt(rawMessage.AgentID, 10),
        Event: rawMessage.Event,
      };
      this.logger.log('接收并解密消息成功:', messageDto);

      // 3. 处理业务逻辑
      const wecomUserId = messageDto.FromUserName;
      if (!wecomUserId) {
        this.logger.warn('消息中缺少 FromUserName', messageDto);
        return;
      }

      // 4. 检查用户是否存在，不存在则创建
      let user = await this.userService.findOne(wecomUserId);
      if (!user) {
        this.logger.log(`用户 ${wecomUserId} 不存在，将创建新用户`);
        user = await this.userService.create({
          wecomUserId: wecomUserId,
          name: wecomUserId, // 暂时使用 wecomUserId 作为 name
        });
        this.logger.log(`用户 ${wecomUserId} 创建成功`, user);
      } else {
        this.logger.log(`用户 ${wecomUserId} 已存在`, user);
      }

      // 5. 检查用户使用量
      const subscription = await this.prisma.subscription.findFirst({
        where: {
          user_id: user.id,
          status: 'active',
          end_date: { gte: new Date() },
        },
        orderBy: { end_date: 'desc' },
      });

      if (!subscription) {
        throw new Error('用户没有有效订阅');
      }

      if (user.usageCount >= (subscription.limit || 1000)) {
        throw new Error('超出订阅使用限额');
      }

      // 增加使用计数
      await this.userService.incrementUsage(user.id);

      // 6. 打印消息内容，为后续记账做准备
      if (messageDto.MsgType === 'text' && messageDto.Content) {
        this.logger.log(
          `用户 ${wecomUserId} 发送了文本消息: ${messageDto.Content}`,
        );

        // 解析消息内容为账单数据
        const billingData = this.parseBillingData(messageDto.Content);

        try {
          // 获取用户配置中的飞书文档ID
          const userConfig = await this.userService.getUserConfig(wecomUserId);

          if (userConfig?.feishuDocId) {
            // 记录到飞书文档
            await this.feishuService.addRecordToDocument(
              userConfig.feishuDocId,
              billingData,
            );
            this.logger.log(
              `成功记录账单到飞书文档: ${userConfig.feishuDocId}`,
            );
          }
        } catch (error) {
          this.logger.error('飞书记录失败', error.stack);
        }
      }
    } catch (error) {
      this.logger.error('处理企业微信消息时发生错误', error.stack);
      throw error; // 向上抛出异常，让 Controller 捕获
    }
  }
  /**
   * 解析消息内容为账单数据
   * @param content 消息内容
   */
  private parseBillingData(content: string): {
    amount: number;
    category: string;
    date: string;
    description: string;
  } {
    // 示例解析逻辑：格式为"金额 类别 描述"
    const parts = content.split(' ');
    if (parts.length < 2) {
      throw new Error('消息格式不正确，请使用"金额 类别 描述"格式');
    }

    return {
      amount: parseFloat(parts[0]),
      category: parts[1],
      date: new Date().toISOString().split('T')[0], // 当前日期
      description: parts.slice(2).join(' ') || '无描述',
    };
  }
}

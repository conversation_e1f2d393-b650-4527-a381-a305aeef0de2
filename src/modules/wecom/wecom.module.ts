import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { WecomController } from './wecom.controller';
import { WecomService } from './wecom.service';
import { UserModule } from './user/user.module';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true, // 使 ConfigService 在全局可用
    }),
    UserModule, // 导入 UserModule 以使用 UserService
    PrismaModule, // 导入 PrismaModule 以使用 PrismaService
  ],
  controllers: [WecomController],
  providers: [WecomService],
})
export class WecomModule {}
